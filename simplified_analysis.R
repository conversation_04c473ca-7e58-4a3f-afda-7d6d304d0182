# 英国可持续肉类选择偏好研究 - 简化分析
# Research: Preferences for sustainable meat choice in the UK. Does information matter?

# 加载必要的包
library(readxl)
library(dplyr)
library(tidyr)
library(stringr)
library(mlogit)
library(gmnl)
library(ggplot2)

# ===== 数据准备 =====
print("开始数据准备...")

# 读取数据
raw <- read_excel("data.xlsx", sheet = 1)
title <- raw[1:2, ]
raw <- raw[-c(1, 2), ]

# 处理treatment变量
raw <- raw %>%
  rename(Q7 = starts_with("Q7. Cheap talk")) %>%
  mutate(Q7_trim = str_trim(Q7)) %>%
  mutate(treatment = case_when(
    is.na(Q7_trim) ~ NA_integer_,
    Q7_trim == "-" ~ 0L,
    str_starts(Q7_trim, "Yes") ~ 1L,
    TRUE ~ 0L
  ))

print("Treatment分布:")
print(table(raw$treatment, useNA = "ifany"))

# 重命名选择题列
names(raw)[21:28] <- paste0("Q", 1:8, "_choice")

# 读取设计矩阵
design <- read.csv("design_matrix.csv", stringsAsFactors = FALSE)

# 准备DCE数据
dce_raw <- raw %>%
  select(respondent_id = UserNo, treatment, Q1_choice:Q8_choice)

# 转换为长格式
dce_long <- dce_raw %>%
  pivot_longer(
    cols = Q1_choice:Q8_choice,
    names_to = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(task_id = as.integer(str_extract(qn, "\\d+"))) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

# 清理选择数据
dce_long <- dce_long %>%
  mutate(chosen_Option = str_extract(chosen_Option, "[ABC]")) %>%
  filter(!is.na(chosen_Option)) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

# 创建完整的选择集
resp_tasks <- dce_long %>%
  distinct(respondent_id, task_id, treatment)

options <- design %>% distinct(Option)
resp_task_options <- crossing(resp_tasks, options)

# 合并设计矩阵
df_model <- resp_task_options %>%
  left_join(design, by = c("task_id" = "Task", "Option" = "Option"))

# 添加选择标记
dce_flags <- dce_long %>%
  transmute(respondent_id, task_id, Option = chosen_Option, choice_flag = 1L)

df_model <- df_model %>%
  left_join(dce_flags, by = c("respondent_id", "task_id", "Option")) %>%
  mutate(choice = replace_na(choice_flag, 0L)) %>%
  select(-choice_flag)

# 创建虚拟变量
df_model <- df_model %>%
  mutate(
    ASC_C = as.integer(Option == "C"),
    MeatType = ifelse(Option == "C", NA, MeatType),
    LivestockEffect = ifelse(Option == "C", NA, LivestockEffect),
    AntibioticUse = ifelse(Option == "C", NA, AntibioticUse),
    meat_Cultured = as.integer(MeatType == "Cultured"),
    meat_PlantBased = as.integer(MeatType == "Plant based"),
    impact_Medium = as.integer(LivestockEffect == "Medium"),
    impact_High = as.integer(LivestockEffect == "High"),
    ab_medium = as.integer(AntibioticUse == "Medium"),
    ab_high = as.integer(AntibioticUse == "High")
  ) %>%
  replace_na(list(
    meat_Cultured = 0, meat_PlantBased = 0,
    impact_Medium = 0, impact_High = 0,
    ab_medium = 0, ab_high = 0
  ))

# 准备mlogit数据
df_model_clean <- df_model %>%
  distinct(respondent_id, task_id, Option, .keep_all = TRUE) %>%
  mutate(
    chid = paste(respondent_id, task_id, sep = "_"),
    choice = choice == 1
  )

# 创建mlogit数据格式
mlogit_df <- mlogit.data(
  df_model_clean,
  choice = "choice",
  shape = "long",
  alt.var = "Option",
  chid.var = "chid",
  id.var = "respondent_id"
)

print("数据准备完成!")
print(paste("总观测数:", nrow(mlogit_df)))
print(paste("受访者数:", length(unique(mlogit_df$respondent_id))))

# 分治疗组数据
mlogit_df_control <- mlogit_df[mlogit_df$treatment == 0, ]
mlogit_df_treatment <- mlogit_df[mlogit_df$treatment == 1, ]

print(paste("控制组观测数:", nrow(mlogit_df_control)))
print(paste("处理组观测数:", nrow(mlogit_df_treatment)))

# ===== 基础条件Logit模型 =====
print("估计基础条件Logit模型...")

# 控制组模型
clogit_control <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased + impact_Medium + impact_High + 
           ab_medium + ab_high + Price | 1,
  data = mlogit_df_control
)

# 处理组模型
clogit_treatment <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased + impact_Medium + impact_High + 
           ab_medium + ab_high + Price | 1,
  data = mlogit_df_treatment
)

print("基础模型估计完成!")

# ===== 表1: 基础模型比较 =====
create_table1 <- function(model_control, model_treatment) {
  coef_control <- summary(model_control)$CoefTable
  coef_treatment <- summary(model_treatment)$CoefTable
  
  table1 <- data.frame(
    Variable = rownames(coef_control),
    Control_Coef = round(coef_control[, 1], 4),
    Control_SE = round(coef_control[, 2], 4),
    Control_Pval = round(coef_control[, 4], 4),
    Treatment_Coef = round(coef_treatment[, 1], 4),
    Treatment_SE = round(coef_treatment[, 2], 4),
    Treatment_Pval = round(coef_treatment[, 4], 4)
  )
  
  return(table1)
}

table1 <- create_table1(clogit_control, clogit_treatment)
print("表1: 条件Logit模型估计结果")
print(table1)

# ===== 表2: 支付意愿计算 =====
calc_wtp <- function(model) {
  coef_table <- summary(model)$CoefTable
  price_coef <- coef_table["Price", 1]
  
  if (abs(price_coef) < 1e-6) {
    print("警告: 价格系数接近零，WTP计算可能不准确")
    return(NULL)
  }
  
  attributes <- c("meat_Cultured", "meat_PlantBased", "impact_Medium", 
                 "impact_High", "ab_medium", "ab_high")
  
  wtp_data <- data.frame(
    Attribute = attributes,
    WTP = round(-coef_table[attributes, 1] / price_coef, 2),
    SE = round(coef_table[attributes, 2] / abs(price_coef), 2)
  )
  
  return(wtp_data)
}

wtp_control <- calc_wtp(clogit_control)
wtp_treatment <- calc_wtp(clogit_treatment)

if (!is.null(wtp_control) && !is.null(wtp_treatment)) {
  table2 <- wtp_control %>%
    rename(Control_WTP = WTP, Control_SE = SE) %>%
    left_join(wtp_treatment %>% rename(Treatment_WTP = WTP, Treatment_SE = SE),
              by = "Attribute") %>%
    mutate(Difference = round(Treatment_WTP - Control_WTP, 2))
  
  print("表2: 支付意愿比较")
  print(table2)
} else {
  print("无法计算WTP - 价格系数问题")
}

# ===== 表3: 市场份额分析 =====
calc_market_share <- function(data) {
  market_share <- data %>%
    filter(choice == TRUE, Option != "C") %>%
    group_by(MeatType) %>%
    summarise(n_choices = n(), .groups = "drop") %>%
    mutate(Market_Share = round(n_choices / sum(n_choices), 4)) %>%
    select(MeatType, Market_Share)
  
  return(market_share)
}

ms_control <- calc_market_share(mlogit_df_control)
ms_treatment <- calc_market_share(mlogit_df_treatment)

table3 <- ms_control %>%
  rename(Control_Share = Market_Share) %>%
  full_join(ms_treatment %>% rename(Treatment_Share = Market_Share), 
            by = "MeatType") %>%
  mutate(
    Control_Share = ifelse(is.na(Control_Share), 0, Control_Share),
    Treatment_Share = ifelse(is.na(Treatment_Share), 0, Treatment_Share),
    Difference = round(Treatment_Share - Control_Share, 4)
  )

print("表3: 市场份额分析")
print(table3)

# 计算opt-out率
optout_control <- mean(mlogit_df_control$choice[mlogit_df_control$Option == "C"])
optout_treatment <- mean(mlogit_df_treatment$choice[mlogit_df_treatment$Option == "C"])

print(paste("控制组opt-out率:", round(optout_control, 4)))
print(paste("处理组opt-out率:", round(optout_treatment, 4)))

print("基础分析完成!")
