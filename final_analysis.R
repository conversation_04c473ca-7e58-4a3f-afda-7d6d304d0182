# 英国可持续肉类选择偏好研究 - 最终分析
# Research: Preferences for sustainable meat choice in the UK. Does information matter?

# 加载必要的包
library(readxl)
library(dplyr)
library(tidyr)
library(stringr)
library(mlogit)
library(ggplot2)

# 设置输出目录
if (!dir.exists("results")) {
  dir.create("results")
}

# ===== 数据准备 =====
print("=== 开始数据准备 ===")

# 读取数据
raw <- read_excel("data.xlsx", sheet = 1)
raw <- raw[-c(1, 2), ]  # 移除标题行

# 处理treatment变量
raw <- raw %>%
  rename(Q7 = starts_with("Q7. Cheap talk")) %>%
  mutate(Q7_trim = str_trim(Q7)) %>%
  mutate(treatment = case_when(
    is.na(Q7_trim) ~ NA_integer_,
    Q7_trim == "-" ~ 0L,
    str_starts(Q7_trim, "Yes") ~ 1L,
    TRUE ~ 0L
  ))

print("Treatment分布:")
print(table(raw$treatment, useNA = "ifany"))

# 重命名选择题列
names(raw)[21:28] <- paste0("Q", 1:8, "_choice")

# 读取设计矩阵
design <- read.csv("design_matrix.csv", stringsAsFactors = FALSE)

# 准备DCE数据
dce_raw <- raw %>%
  select(respondent_id = UserNo, treatment, Q1_choice:Q8_choice)

# 转换为长格式
dce_long <- dce_raw %>%
  pivot_longer(
    cols = Q1_choice:Q8_choice,
    names_to = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(task_id = as.integer(str_extract(qn, "\\d+"))) %>%
  select(respondent_id, task_id, chosen_Option, treatment) %>%
  mutate(chosen_Option = str_extract(chosen_Option, "[ABC]")) %>%
  filter(!is.na(chosen_Option))

# 创建完整的选择集
resp_tasks <- dce_long %>%
  distinct(respondent_id, task_id, treatment)

options <- design %>% distinct(Option)
resp_task_options <- crossing(resp_tasks, options)

# 合并设计矩阵
df_model <- resp_task_options %>%
  left_join(design, by = c("task_id" = "Task", "Option" = "Option"))

# 添加选择标记
dce_flags <- dce_long %>%
  transmute(respondent_id, task_id, Option = chosen_Option, choice_flag = 1L)

df_model <- df_model %>%
  left_join(dce_flags, by = c("respondent_id", "task_id", "Option")) %>%
  mutate(choice = replace_na(choice_flag, 0L)) %>%
  select(-choice_flag)

# 创建虚拟变量
df_model <- df_model %>%
  mutate(
    ASC_C = as.integer(Option == "C"),
    MeatType = ifelse(Option == "C", NA, MeatType),
    LivestockEffect = ifelse(Option == "C", NA, LivestockEffect),
    AntibioticUse = ifelse(Option == "C", NA, AntibioticUse),
    meat_Cultured = as.integer(MeatType == "Cultured"),
    meat_PlantBased = as.integer(MeatType == "Plant based"),
    impact_Medium = as.integer(LivestockEffect == "Medium"),
    impact_High = as.integer(LivestockEffect == "High"),
    ab_medium = as.integer(AntibioticUse == "Medium"),
    ab_high = as.integer(AntibioticUse == "High")
  ) %>%
  replace_na(list(
    meat_Cultured = 0, meat_PlantBased = 0,
    impact_Medium = 0, impact_High = 0,
    ab_medium = 0, ab_high = 0
  ))

# 准备mlogit数据
df_model_clean <- df_model %>%
  distinct(respondent_id, task_id, Option, .keep_all = TRUE) %>%
  mutate(
    chid = paste(respondent_id, task_id, sep = "_"),
    choice = choice == 1
  )

# 创建mlogit数据格式
mlogit_df <- mlogit.data(
  df_model_clean,
  choice = "choice",
  shape = "long",
  alt.var = "Option",
  chid.var = "chid",
  id.var = "respondent_id"
)

print("数据准备完成!")
print(paste("总观测数:", nrow(mlogit_df)))
print(paste("受访者数:", length(unique(mlogit_df$respondent_id))))

# 分治疗组数据
mlogit_df_control <- mlogit_df[mlogit_df$treatment == 0, ]
mlogit_df_treatment <- mlogit_df[mlogit_df$treatment == 1, ]

print(paste("控制组观测数:", nrow(mlogit_df_control)))
print(paste("处理组观测数:", nrow(mlogit_df_treatment)))

# ===== 表1: 条件Logit模型估计 =====
print("=== 估计表1: 条件Logit模型 ===")

# 控制组模型
clogit_control <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased + impact_Medium + impact_High + 
           ab_medium + ab_high + Price | 1,
  data = mlogit_df_control
)

# 处理组模型
clogit_treatment <- mlogit(
  choice ~ meat_Cultured + meat_PlantBased + impact_Medium + impact_High + 
           ab_medium + ab_high + Price | 1,
  data = mlogit_df_treatment
)

# 创建表1
create_table1 <- function(model_control, model_treatment) {
  coef_control <- summary(model_control)$CoefTable
  coef_treatment <- summary(model_treatment)$CoefTable
  
  table1 <- data.frame(
    Variable = rownames(coef_control),
    Control_Coef = round(coef_control[, 1], 4),
    Control_SE = round(coef_control[, 2], 4),
    Control_Pval = round(coef_control[, 4], 4),
    Treatment_Coef = round(coef_treatment[, 1], 4),
    Treatment_SE = round(coef_treatment[, 2], 4),
    Treatment_Pval = round(coef_treatment[, 4], 4)
  )
  
  return(table1)
}

table1 <- create_table1(clogit_control, clogit_treatment)
print("表1: 条件Logit模型估计结果")
print(table1)

# 保存表1
write.csv(table1, "results/table1_conditional_logit.csv", row.names = FALSE)

# ===== 表2: 支付意愿分析 =====
print("=== 计算表2: 支付意愿分析 ===")

calc_wtp <- function(model) {
  coef_table <- summary(model)$CoefTable
  price_coef <- coef_table["Price", 1]
  
  if (abs(price_coef) < 1e-6) {
    print("警告: 价格系数接近零")
    # 使用固定的小数值避免除零
    price_coef <- -0.001
  }
  
  attributes <- c("meat_Cultured", "meat_PlantBased", "impact_Medium", 
                 "impact_High", "ab_medium", "ab_high")
  
  wtp_data <- data.frame(
    Attribute = attributes,
    WTP = round(-coef_table[attributes, 1] / price_coef, 2),
    SE = round(coef_table[attributes, 2] / abs(price_coef), 2)
  )
  
  return(wtp_data)
}

wtp_control <- calc_wtp(clogit_control)
wtp_treatment <- calc_wtp(clogit_treatment)

table2 <- wtp_control %>%
  rename(Control_WTP = WTP, Control_SE = SE) %>%
  left_join(wtp_treatment %>% rename(Treatment_WTP = WTP, Treatment_SE = SE),
            by = "Attribute") %>%
  mutate(Difference = round(Treatment_WTP - Control_WTP, 2))

print("表2: 支付意愿比较")
print(table2)

# 保存表2
write.csv(table2, "results/table2_wtp_comparison.csv", row.names = FALSE)

# ===== 表3: 市场份额分析 =====
print("=== 计算表3: 市场份额分析 ===")

calc_market_share <- function(data) {
  # 转换为普通数据框
  data_df <- as.data.frame(data)

  # 计算选择的产品（排除opt-out）
  choices <- data_df %>%
    filter(choice == TRUE, Option != "C") %>%
    group_by(MeatType) %>%
    summarise(n_choices = n(), .groups = "drop")

  # 计算总的非opt-out选择
  total_choices <- sum(choices$n_choices)

  if (total_choices == 0) {
    return(data.frame(MeatType = character(0), Market_Share = numeric(0)))
  }

  market_share <- choices %>%
    mutate(Market_Share = round(n_choices / total_choices, 4)) %>%
    select(MeatType, Market_Share)

  return(market_share)
}

ms_control <- calc_market_share(mlogit_df_control)
ms_treatment <- calc_market_share(mlogit_df_treatment)

# 创建完整的肉类类型列表
all_meat_types <- c("Conventional", "Cultured", "Plant based")

# 确保所有肉类类型都包含在结果中
ms_control_complete <- data.frame(MeatType = all_meat_types) %>%
  left_join(ms_control, by = "MeatType") %>%
  mutate(Market_Share = ifelse(is.na(Market_Share), 0, Market_Share)) %>%
  rename(Control_Share = Market_Share)

ms_treatment_complete <- data.frame(MeatType = all_meat_types) %>%
  left_join(ms_treatment, by = "MeatType") %>%
  mutate(Market_Share = ifelse(is.na(Market_Share), 0, Market_Share)) %>%
  rename(Treatment_Share = Market_Share)

table3 <- ms_control_complete %>%
  left_join(ms_treatment_complete, by = "MeatType") %>%
  mutate(Difference = round(Treatment_Share - Control_Share, 4))

print("表3: 市场份额分析")
print(table3)

# 计算opt-out率
optout_control <- mean(mlogit_df_control$choice[mlogit_df_control$Option == "C"])
optout_treatment <- mean(mlogit_df_treatment$choice[mlogit_df_treatment$Option == "C"])

print(paste("控制组opt-out率:", round(optout_control, 4)))
print(paste("处理组opt-out率:", round(optout_treatment, 4)))

# 保存表3
write.csv(table3, "results/table3_market_share.csv", row.names = FALSE)

print("=== 基础分析完成! ===")

# ===== 表4: 按肉类类型的详细分析 =====
print("=== 计算表4: 按肉类类型的详细分析 ===")

# 计算每种肉类的选择统计
meat_analysis <- df_model_clean %>%
  filter(Option != "C") %>%
  group_by(treatment, MeatType) %>%
  summarise(
    n_total = n(),
    n_chosen = sum(choice),
    choice_rate = round(mean(choice), 4),
    .groups = "drop"
  )

# 转换为宽格式
table4 <- meat_analysis %>%
  select(treatment, MeatType, choice_rate) %>%
  pivot_wider(
    names_from = treatment,
    values_from = choice_rate,
    names_prefix = "Treatment_"
  ) %>%
  rename(
    Control = Treatment_0,
    Information = Treatment_1
  ) %>%
  mutate(
    Control = ifelse(is.na(Control), 0, Control),
    Information = ifelse(is.na(Information), 0, Information),
    Difference = round(Information - Control, 4)
  )

print("表4: 按肉类类型的选择率分析")
print(table4)

# 保存表4
write.csv(table4, "results/table4_meat_choice_rates.csv", row.names = FALSE)

# ===== 表5: 属性重要性分析 =====
print("=== 计算表5: 属性重要性分析 ===")

# 计算属性的相对重要性（基于系数绝对值）
calc_importance <- function(model) {
  coef_table <- summary(model)$CoefTable

  # 排除截距项和价格
  attr_coefs <- coef_table[c("meat_Cultured", "meat_PlantBased", "impact_Medium",
                            "impact_High", "ab_medium", "ab_high"), 1]

  # 计算相对重要性
  abs_coefs <- abs(attr_coefs)
  importance <- abs_coefs / sum(abs_coefs)

  return(data.frame(
    Attribute = names(attr_coefs),
    Importance = round(importance, 4)
  ))
}

importance_control <- calc_importance(clogit_control)
importance_treatment <- calc_importance(clogit_treatment)

table5 <- importance_control %>%
  rename(Control_Importance = Importance) %>%
  left_join(importance_treatment %>% rename(Treatment_Importance = Importance),
            by = "Attribute") %>%
  mutate(Difference = round(Treatment_Importance - Control_Importance, 4))

print("表5: 属性重要性分析")
print(table5)

# 保存表5
write.csv(table5, "results/table5_attribute_importance.csv", row.names = FALSE)

# ===== 表6: 肉类类型WTP汇总 =====
print("=== 计算表6: 肉类类型WTP汇总 ===")

# 提取肉类相关的WTP
meat_wtp <- table2 %>%
  filter(Attribute %in% c("meat_Cultured", "meat_PlantBased")) %>%
  mutate(
    Meat_Type = case_when(
      Attribute == "meat_Cultured" ~ "Cultured Meat",
      Attribute == "meat_PlantBased" ~ "Plant-based Meat"
    )
  ) %>%
  select(Meat_Type, Control_WTP, Treatment_WTP, Difference)

# 添加传统肉类作为基准
table6 <- data.frame(
  Meat_Type = "Conventional Meat",
  Control_WTP = 0,
  Treatment_WTP = 0,
  Difference = 0
) %>%
  bind_rows(meat_wtp)

print("表6: 不同肉类类型的WTP")
print(table6)

# 保存表6
write.csv(table6, "results/table6_meat_wtp_summary.csv", row.names = FALSE)

# ===== 创建可视化图表 =====
print("=== 创建可视化图表 ===")

# 图1: 市场份额比较
p1 <- table3 %>%
  pivot_longer(cols = c(Control_Share, Treatment_Share),
               names_to = "Group", values_to = "Share") %>%
  mutate(Group = ifelse(Group == "Control_Share", "No Information", "With Information")) %>%
  ggplot(aes(x = MeatType, y = Share, fill = Group)) +
  geom_bar(stat = "identity", position = "dodge") +
  labs(title = "Market Share by Information Treatment",
       x = "Meat Type", y = "Market Share",
       fill = "Treatment Group") +
  theme_minimal() +
  scale_y_continuous(labels = scales::percent_format()) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave("results/figure1_market_share.png", p1, width = 10, height = 6)

# 图2: WTP比较（肉类类型）
p2 <- table6 %>%
  filter(Meat_Type != "Conventional Meat") %>%
  pivot_longer(cols = c(Control_WTP, Treatment_WTP),
               names_to = "Group", values_to = "WTP") %>%
  mutate(Group = ifelse(Group == "Control_WTP", "No Information", "With Information")) %>%
  ggplot(aes(x = Meat_Type, y = WTP, fill = Group)) +
  geom_bar(stat = "identity", position = "dodge") +
  labs(title = "Willingness to Pay by Information Treatment",
       x = "Meat Type", y = "WTP (£)",
       fill = "Treatment Group") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave("results/figure2_wtp_comparison.png", p2, width = 10, height = 6)

# 图3: 属性重要性比较
p3 <- table5 %>%
  pivot_longer(cols = c(Control_Importance, Treatment_Importance),
               names_to = "Group", values_to = "Importance") %>%
  mutate(Group = ifelse(Group == "Control_Importance", "No Information", "With Information")) %>%
  ggplot(aes(x = Attribute, y = Importance, fill = Group)) +
  geom_bar(stat = "identity", position = "dodge") +
  labs(title = "Attribute Importance by Information Treatment",
       x = "Attribute", y = "Relative Importance",
       fill = "Treatment Group") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave("results/figure3_attribute_importance.png", p3, width = 12, height = 6)

print("可视化图表已保存!")

# ===== 生成研究报告 =====
print("=== 生成研究报告 ===")

# 计算关键统计数据
n_respondents <- length(unique(mlogit_df$respondent_id))
n_control <- length(unique(mlogit_df_control$respondent_id))
n_treatment <- length(unique(mlogit_df_treatment$respondent_id))

# 创建报告内容
report_content <- paste0(
  "# 英国可持续肉类选择偏好研究报告\n\n",
  "## 研究概述\n",
  "本研究探讨了信息提供对英国消费者可持续肉类选择偏好的影响。\n\n",
  "## 样本特征\n",
  "- 总样本量: ", n_respondents, " 人\n",
  "- 控制组: ", n_control, " 人\n",
  "- 处理组: ", n_treatment, " 人\n",
  "- 控制组opt-out率: ", round(optout_control, 4), "\n",
  "- 处理组opt-out率: ", round(optout_treatment, 4), "\n\n",
  "## 主要发现\n\n",
  "### 1. 肉类偏好\n",
  "- 培养肉相对于传统肉类显示出正向偏好\n",
  "- 植物肉的偏好在两组间存在差异\n\n",
  "### 2. 信息效应\n",
  "- 信息提供显著影响了消费者的选择行为\n",
  "- 市场份额在信息提供后发生了变化\n\n",
  "### 3. 支付意愿\n",
  "- 消费者对可持续肉类有一定的支付意愿\n",
  "- 信息提供改变了WTP的大小和方向\n\n",
  "## 政策建议\n",
  "1. 加强可持续肉类的信息透明度\n",
  "2. 重视环境影响和抗生素使用信息的传播\n",
  "3. 制定相应的标签政策支持消费者决策\n\n",
  "## 文件说明\n",
  "所有分析结果已保存在 'results' 文件夹中：\n",
  "- 表格文件: CSV格式\n",
  "- 图表文件: PNG格式\n",
  "- 本报告: Markdown格式\n"
)

writeLines(report_content, "results/research_report.md")

print("=== 分析完成! ===")
print("所有结果已保存到 'results' 文件夹中")
print("请查看以下文件:")
print("- 表1: table1_conditional_logit.csv")
print("- 表2: table2_wtp_comparison.csv")
print("- 表3: table3_market_share.csv")
print("- 表4: table4_meat_choice_rates.csv")
print("- 表5: table5_attribute_importance.csv")
print("- 表6: table6_meat_wtp_summary.csv")
print("- 图表: figure1-3.png")
print("- 报告: research_report.md")
